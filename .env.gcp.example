# Google Cloud Platform Environment Configuration
# Copy this file to .env.gcp and update the values for your deployment

# Google Cloud Project Configuration
# REPLACE 'your-gcp-project-id' with your actual Google Cloud Project ID
PROJECT_ID=gen-lang-client-0819985982
REGION=us-central1
ENVIRONMENT=production

# Backend Environment Variables (Cloud Run)
FLASK_ENV=production
SECRET_KEY=your-production-secret-key-32-chars-min
CORS_ORIGINS=https://supplyline-frontend-staging-us-central1.a.run.app

# Database Configuration (Cloud SQL)
DB_HOST=/cloudsql/your-project-id:us-central1:supplyline-db
DB_USER=supplyline_user
DB_PASSWORD=your-secure-database-password
DB_NAME=supplyline

# Session Configuration
SESSION_VALIDATE_IP=false
PYTHONDONTWRITEBYTECODE=1
PYTHONUNBUFFERED=1

# Security Configuration
MAX_FAILED_ATTEMPTS=5
INITIAL_LOCKOUT_MINUTES=15
LOCKOUT_MULTIPLIER=2
MAX_LOCKOUT_MINUTES=60

# Frontend Environment Variables
VITE_API_URL=https://supplyline-backend-staging-us-central1.a.run.app

# Cloud Build Substitution Variables
_ENVIRONMENT=staging
_REGION=us-central1
_SECRET_KEY=your-production-secret-key
_CLOUDSQL_INSTANCE=your-project-id:us-central1:supplyline-db

# Resource Limits (Cloud Run)
BACKEND_CPU_LIMIT=1
BACKEND_MEMORY_LIMIT=1Gi
FRONTEND_CPU_LIMIT=0.5
FRONTEND_MEMORY_LIMIT=512Mi

# Monitoring and Logging
ENABLE_CLOUD_LOGGING=true
LOG_LEVEL=INFO

# Backup Configuration
BACKUP_SCHEDULE=daily
BACKUP_RETENTION_DAYS=30
